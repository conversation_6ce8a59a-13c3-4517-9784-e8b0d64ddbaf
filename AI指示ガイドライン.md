---
puppeteer:
  displayHeaderFooter: true
  headerTemplate: "<div style='font-size: 10px; text-align: left; width: 100%;padding-left: 1cm;'></div>"
  footerTemplate: "<div style='font-size: 10px; text-align: center; width: 100%;'><span class='pageNumber'></span> / <span class='totalPages'></span></div>"
  margin:
    top: "1cm"
    right: "1cm"
    bottom: "0.5cm"
    left: "1cm"
  format: "A4"
  printBackground: true
  landscape: false
---

# AI指示ガイドライン

## 1. はじめに

このガイドラインは、AIに対して効果的に指示を出すための参考資料です。システムのカスタマイズや機能追加・変更を依頼する際に、明確で具体的な指示を出すことで、AIがより正確に要望を理解し、適切な対応を行うことができます。

## 2. AIへの指示方法の基本

AIに指示を出す際は、対応内容に応じて以下の点を明確にすることが重要です：

1. **対象の会社名とカスタマイズキー**：どの会社向けの変更かを明示する
2. **ファイル情報**：対象となるファイルのパスや名前を明記する
3. **対象の画面や機能**：変更したい画面や機能の名称を具体的に指定する
4. **変更内容**：追加・変更・削除したい内容を詳細に説明する
5. **データベース情報**：変更が必要なテーブルやカラムの情報を提供する
6. **入力仕様**：新規追加や変更する入力項目の仕様（必須/任意、最大文字数、パターンチェックなど）を明記する
7. **表示仕様**：UI要素の表示方法（ラベル、入力欄タイプ、レイアウトなど）を具体的に記述する
8. **処理フロー**：変更が必要な処理の流れやロジックを説明する
9. **期待する動作**：変更後にどのような動作を期待するかを説明する
10. **現状の仕様や問題点**：現在の動作や発生している問題を説明する
11. **他画面・帳票への影響範囲**：変更が他の画面や帳票に影響を与えるかどうかを明示する
12. **テスト観点・確認方法**：変更後の動作確認方法を具体的に記述する
13. **その他の制約・要望**：コーディングスタイルやコメント要件など、特別な注意点があれば記載する

## 3. 開発作業依頼のためのガイドライン

AIに対して効果的に開発作業を依頼するためのガイドラインです。作業タイプ別のテンプレートと指示例を提供します。

### 3.1 共通の指示情報

開発作業を依頼する場合でも、以下のような情報を含めてください：

1. **ファイル情報**
   - 対象ファイルパス（Smartyテンプレート、JavaScript、PHP等）

2. **対象画面・機能の特定**
   - 画面名（例：顧客情報画面）
   - URLパス（例：juchu/customerinfo/input）
   - 帳票の場合は帳票名（例：発注一覧PDF）
   - 対象ファイルパス（Smartyテンプレート、JavaScript、PHP等）

3. **変更内容の詳細**
   - 追加・変更・削除したい内容
   - データ項目名やDBカラム名
   - UI要素の配置や表示条件

4. **期待する動作・仕様**
   - 変更後の動作
   - 表示条件や制御ロジック
   - UI仕様（レイアウト、色、サイズなど）

5. **現状の仕様・問題点**
   - 現在の動作
   - 発生している問題
   - 再現手順（バグ修正の場合）

6. **他画面・他帳票への影響範囲**
   - 影響を受ける画面・帳票
   - 影響がない場合はその旨を明記

7. **テスト観点・確認方法**
   - テスト対象画面
   - 確認手順
   - 期待値

8. **その他の制約・要望**
   - コーディングスタイル
   - コメント要件
   - その他注意点

### 3.2 作業タイプ別テンプレート

#### 3.2.1 新規画面追加の場合

```

【ファイル情報】
- Smartyテンプレートファイル
- JavaScriptファイル
- CSSファイル
- PHPファイル

【画面概要】
- 画面の目的と機能の概要
- 画面の位置づけ（メニュー構成上の位置）

【画面仕様】
- 画面レイアウト（可能であればモックアップや参考画面）
- 入力項目一覧（項目名、入力タイプ、必須/任意、バリデーションルール）
- ボタン操作と処理内容

【データベース情報】
- 使用するテーブルとカラム
- テーブル間の関連

【処理フロー】
- 画面表示時の初期処理
- 各ボタン操作時の処理
- バリデーション処理
- データ保存処理

【テスト観点・確認方法】
- テスト手順と期待される結果

【参考コード】
- 類似機能を持つ既存画面のファイルパス

【その他の制約・要望】
- コーディングスタイル・命名規則等
```

#### 3.2.2 既存機能改修の場合

```
【対象画面】
- 画面名と機能概要
- 対象ファイルパス（Smartyテンプレート、JavaScript、PHP等）

【改修内容】
- 追加/変更/削除する項目や機能の詳細
- 変更前と変更後の動作の違い

【データベース変更】
- 追加/変更/削除するテーブルやカラム
- データ移行が必要な場合はその方法

【UI変更】
- レイアウト変更の詳細
- 新しい入力項目の仕様（ラベル、入力タイプ、バリデーション等）

【処理フロー変更】
- 変更が必要な処理の詳細
- 新しい処理フローの説明

【テスト観点・確認方法】
- テスト手順と期待される結果

【その他の制約・要望】
- コーディングスタイル・命名規則等
```

#### 3.2.3 バグ対応の場合

```
【会社名・カスタマイズキー】
株式会社〇〇（カスタマイズキー）

【バグ概要】
- バグの症状と影響範囲
- 再現手順

【発生環境】
- 発生するブラウザやデバイス
- 特定のデータや条件

【期待される動作】
- 正常時の期待される動作

【調査結果】
- すでに調査済みの内容や仮説
- 関連すると思われるコード箇所

【修正方針】
- 考えられる修正アプローチ（もしあれば）

【テスト観点・確認方法】
- テスト手順と期待される結果

【その他の制約・要望】
- コーディングスタイル・命名規則等
```

### 3.3 指示例
#### 3.3.1 機能追加の例

```
お客様情報入力画面の受付情報タブの「寺院連絡事項」項目の下に「備考」項目を追加してください。

【ファイル情報】
- Smartyテンプレートファイル: application\views\smarty\juchu\customerinfo\customerinfo-sano.tpl
- JavaScriptファイル: public_dev\js\app\juchu\juchu.customerinfo.sano.js
- CSSファイル: public_dev\css\app\juchu\juchu.customerinfo.sano.css
- PHPファイル: application\modules\juchu\controllers\Juchu\JuchuCustomerinfo.sano_k.php

【データベース情報】
- 保存先テーブル: seko_kihon_info
- 保存先カラム: v_free30 (VARCHAR(100))

【入力仕様】
- 必須チェック: 不要
- 最大文字数: 100文字
- パターンチェック: 不要

【表示仕様】
- ラベル表示: 「備考」
- 入力欄タイプ: テキストエリア（5行表示）
- 幅: ラベルと入力項目の幅と高さは 「寺院連絡事項」にあわせてください

【実装上の注意点】
- 既存のデザインとコードスタイルを踏襲すること
- 他の入力項目と同じCSSクラスを使用すること
- 既存のバリデーション処理の流れに沿って実装すること
- コメントは日本語で記述すること
```

#### 3.3.2 バグ修正の例
会社名指定する場合、カスタイマイズキーを含むプロジェクトルールをAIに設定する必要があります
```
【バグ対応依頼】

【会社名・カスタマイズキー】
株式会社ごんきや（gonkiya_k,gonkiya,sanmen,sano）

【対象画面・機能】
発注一覧画面（juchu/hachu/list）

【変更内容】
発注日でソートした際に正しく並び替えられない問題を修正

【期待する動作】
・発注日カラムをクリックすると日付順に正しくソートされる
・昇順・降順の切り替えも正常に動作する

【現状の仕様・問題点】
発注日でソートすると日付の大小関係が無視され、文字列としてソートされている

【他画面・帳票への影響範囲】
発注一覧画面のソート機能のみの修正、他画面への影響なし

【テスト観点・確認方法】
・発注日でソートして日付順に並ぶことを確認
・昇順・降順の切り替えが正常に動作することを確認

【その他の制約・要望】
既存のソート処理ロジックを修正、新規ロジックの追加は避ける
```

#### 3.3.3 レイアウト変更の例
```
【既存機能改修依頼】

【会社名・カスタマイズキー】
株式会社彩苑（saien2,saien,sano）

【対象画面・機能】
施行カレンダー画面（juchu/calendar/index）

【変更内容】
土日祝日の背景色を変更
・土曜日：水色（#E6F3FF）
・日曜：薄い赤色（#FFE6E6）

【期待する動作】
・カレンダー表示時に土日の背景色が指定色で表示される

【現状の仕様・問題点】
土日の背景色が区別されていない

【他画面・帳票への影響範囲】
施行カレンダー画面のみの変更、他画面への影響なし

【テスト観点・確認方法】
・土曜日が水色で表示されることを確認
・日曜が薄い赤色で表示されることを確認
・平日の背景色は変更されないことを確認

【その他の制約・要望】
既存のCSSクラスを活用し、新規クラスの追加は最小限に
```

<!-- #### 3.3.4 返礼品発注画面への備考項目追加例
```
【既存機能改修依頼】

【対象画面】
- 画面名：返礼品発注画面
- 対象ファイルパス：
  - Smartyテンプレート: application\views\smarty\hachu\hachushori\henrei-sano.tpl
  - JavaScriptファイル: public_dev\js\app\hachu\hachu.henrei.sano.js
  - CSSファイル: public_dev\css\app\hachu\hachu.henrei.sano.css
  - PHPファイル: application\modules\hachu\controllers\HenreidataController.php

【改修内容】
- 返礼品発注画面の最後に全体「備考」項目を追加
- 入力された備考をデータベースに保存する処理を実装

【データベース情報】
- 保存先テーブル: seko_kanri_info
- 保存先カラム: v_free2 (VARCHAR(300))
- 制約：seko_kanri_infoのdata_sbt=3,item_no=1が返礼品発注書のレコード

【入力仕様】
- 必須チェック: 不要
- 最大文字数: 200文字
- パターンチェック: 不要

【表示仕様】
- ラベル表示: 「備考」
- 入力欄タイプ: テキストエリア
- レイアウト: application\views\smarty\hachu\hachushori\mohuku-sano.tplの備考にあわせる

【テスト観点・確認方法】
- 備考欄に入力して保存し、再表示したときに入力内容が表示されることを確認
- 文字数制限（200文字）が正しく機能することを確認

【その他の制約・要望】
- 既存のデザインとコードスタイルを踏襲すること
- コメントは日本語で記述すること
``` -->

### 3.4 コード生成の流れ

以下はAIツールを使ったコード生成・修正の基本的な流れです：

**凡例**: 水色は開発者が行う作業、ピンク色はAIが行う作業、緑色は完了状態を表しています。

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'fontSize': '11px', 'fontFamily': 'arial', 'nodeSpacing': 10, 'rankSpacing': 20 }}}%%
flowchart TD
    %% 凡例（先頭に横向きに配置）
    L[開発者] --- M[AI] --- N[完了]
    style L fill:#d4f1f9,stroke:#333,stroke-width:1px
    style M fill:#f9d4f1,stroke:#333,stroke-width:1px
    style N fill:#d5f5e3,stroke:#333,stroke-width:1px

    %% メインフロー
    A[プロンプト作成] --> B[AIへ指示]
    B --> C[AI修正案生成]
    C --> D[差分確認]
    D --> E{適切か?}
    E -->|No| F[プロンプト改善]
    F --> B
    E -->|Yes| G[修正適用]
    G --> H[動作検証]
    H --> I{正常か?}
    I -->|No| J[問題点特定]
    J --> F
    I -->|Yes| K[完了]

    %% コンパクトなレイアウト

    %% 開発者とAIの役割を色分け
    style A fill:#d4f1f9,stroke:#333,stroke-width:1px
    style B fill:#d4f1f9,stroke:#333,stroke-width:1px
    style C fill:#f9d4f1,stroke:#333,stroke-width:1px
    style D fill:#d4f1f9,stroke:#333,stroke-width:1px
    style E fill:#d4f1f9,stroke:#333,stroke-width:1px
    style F fill:#d4f1f9,stroke:#333,stroke-width:1px
    style G fill:#d4f1f9,stroke:#333,stroke-width:1px
    style H fill:#d4f1f9,stroke:#333,stroke-width:1px
    style I fill:#d4f1f9,stroke:#333,stroke-width:1px
    style J fill:#d4f1f9,stroke:#333,stroke-width:1px
    style K fill:#d5f5e3,stroke:#333,stroke-width:1px

    %% 凡例とメインフローを視覚的に分離
    linkStyle 0,1 stroke-dasharray: 5 5
```

このサイクルは修正が完了するまで繰り返されます。特に重要なポイントは：

1. **具体的なプロンプト作成**: 修正内容、ファイル情報、要件を明確に記述
2. **差分確認**: AIが生成した修正案を既存コードと比較して適切か確認
3. **プロンプト改善**: 必要に応じてより詳細な情報や制約条件を追加
4. **動作検証**: 実際の環境で動作確認を行い、問題があれば原因を特定

効率的なコード生成のためには、このサイクルを素早く回すことが重要です。


## 4. 効果的なフィードバックの方法

AIが提案した解決策に対して、以下のようなフィードバックを提供することで、より良い結果を得ることができます：

1. **具体的な改善点の指摘**：「この部分はこのように変更してほしい」と具体的に伝える
2. **良かった点の共有**：うまくいった部分を伝えることで、AIは成功パターンを学習できる
3. **優先順位の明確化**：複数の課題がある場合、どの問題が最も重要かを伝える
4. **追加情報の提供**：AIが見落としている可能性のある情報や制約条件を伝える

## 5. まとめ
このガイドラインに従うことで、AIに対して効果的な指示を出し、開発作業をスムーズに進めることができます。特に、具体的な情報提供と明確な指示が重要です。AIとのコミュニケーションを通じて、より良いシステムを構築していきましょう。
                      


