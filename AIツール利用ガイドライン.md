---
puppeteer:
  displayHeaderFooter: true
  headerTemplate: "<div style='font-size: 10px; text-align: left; width: 100%;padding-left: 1cm;'></div>"
  footerTemplate: "<div style='font-size: 10px; text-align: center; width: 100%;'><span class='pageNumber'></span> / <span class='totalPages'></span></div>"
  margin:
    top: "1cm"
    right: "1cm"
    bottom: "0.5cm"
    left: "1cm"
  format: "A4"
  printBackground: true
  landscape: false
---

# AIツール利用ガイドライン

## 目次

1. [目的](#1-目的)
2. [適用範囲](#2-適用範囲)
3. [AIツール利用の基本方針](#3-aiツール利用の基本方針)
4. [利用可能なAIツール](#4-利用可能なaiツール)
5. [AIツール活用の具体的なシーン](#5-aiツール活用の具体的なシーン)
6. [AIツール利用時の注意事項](#6-aiツール利用時の注意事項)
7. [AIツール活用のワークフロー](#7-aiツール活用のワークフロー)
8. [効果測定と改善](#8-効果測定と改善)
9. [教育とナレッジ共有](#9-教育とナレッジ共有)
10. [ガイドラインの更新](#10-ガイドラインの更新)

## 1. 目的

本ガイドラインは、BS開発部におけるシステム開発でのAIツール活用に関する基本方針と具体的な利用ルールを定めることを目的としています。AIツールを適切に活用することで、開発効率の向上、コード品質の改善、および教育支援の効率化を図ります。

## 2. 適用範囲

本ガイドラインは、BS開発部の全開発メンバーに適用されます。対象となるプロジェクトは、新規、既存のシステム開発および保守作業です。

## 3. AIツール利用の基本方針

### 3.1 AIツール活用の目的
- 開発作業の効率化と生産性向上
- コード品質の向上とバグの削減
- 知識共有と技術スキル向上の支援
- 繰り返し作業の自動化による工数削減
- 個人のAIリテラシーとプロンプトエンジニアリングスキルの向上
- 最新技術動向への適応力強化と将来的な競争力の確保

### 3.2 AIツール選定の基準
- 開発で使用する技術（PHP、JavaScript、HTML、CSS、SQL等）への対応
- コード生成、コード補完、リファクタリング、バグ検出などの機能の充実度
- Enterprise/Business向けプランでのプライバシー保護機能（推奨）
- コスト対効果と使いやすさ

## 4. 利用可能なAIツール

### 4.1 承認済みAIツール
以下のAIツールは、部門として利用を承認しています：

<table style="border-collapse: collapse;">
  <tr style="background-color:rgb(240, 248, 225); color: black;">
    <th>ツール名</th>
    <th>推奨プラン</th>
    <th>主な用途</th>
    <th>備考</th>
  </tr>
  <tr>
    <td>GitHub Copilot</td>
    <td>Business/Enterprise</td>
    <td>コード補完、チャット支援</td>
    <td>VSCode拡張として利用</td>
  </tr>
  <tr style="background-color: #F8FCF5;">
    <td>Cursor</td>
    <td>Business</td>
    <td>コード生成、エージェント機能</td>
    <td>専用エディタとして利用</td>
  </tr>
  <tr>
    <td>Gemini Code Assist</td>
    <td>Standard/Enterprise</td>
    <td>コード補完、チャット支援</td>
    <td>VSCode拡張として利用</td>
  </tr>
  <tr style="background-color: #F8FCF5;">
    <td>Windsurf</td>
    <td>Teams/Enterprise</td>
    <td>コード生成、エージェント機能</td>
    <td>専用エディタとして利用</td>
  </tr>
</table>

### 4.2 利用申請手続き
- 上記以外のAIツールを利用する場合は、事前に開発リーダーへの申請と承認が必要です
- 申請時には、ツール名、用途、利用期間を明記してください

## 5. AIツール活用の具体的なシーン

### 5.1 コード作成・修正
- 新機能実装時のコード生成
- カスタマイズ機能のコード生成
- バグ修正のための解決策提案
- ベストプラクティスに基づいたコード改善
- 繰り返しパターンのコード自動生成

### 5.2 データベース関連
- SQL文の生成と最適化
- クエリパフォーマンスの改善提案

### 5.3 フロントエンド開発
- HTML/CSS/JavaScriptコードの生成
- レスポンシブデザインの実装支援
- フロントエンドとバックエンドの連携コード作成

### 5.4 バックエンド開発
- PHPなどのサーバーサイドコード生成
- データベース操作（取得・更新・登録）処理の実装
- 入力値検証やエラーハンドリングの実装
- 簡単なビジネスロジックの生成
- APIエンドポイントの実装

### 5.5 コード理解と分析
- 既存コードの解析と理解の促進
- コードの流れや依存関係の可視化
- 複雑なロジックの説明と簡略化提案

### 5.6 ドキュメント生成
- コードを参照して仕様書やAPI仕様書を自動生成
- コメントからわかりやすいドキュメントの作成
- 既存コードの動作説明ドキュメントの生成
- テスト仕様書の自動作成
- コードの変更履歴からリリースノートの生成

### 5.7 セキュリティ対策
- 潜在的なセキュリティ脆弱性の検出と予防
- コードベース全体のセキュリティパターン分析
- 複雑なセキュリティ脆弱性の文脈に基づいた検出
- 最新のセキュリティベストプラクティスの自動提案
- セキュアコーディングの学習と知識共有

## 6. AIツール利用時の注意事項

### 6.1 機密情報の取り扱い
- 顧客情報、個人情報、認証情報などの機密データをAIツールに入力しないこと
- やむを得ず機密性の高いコードを送信する場合は、機密情報をマスキングすること
- 機密性の高いプロジェクトでは、事前に開発リーダーと相談の上でAIツールの利用範囲を決定すること

### 6.2 コード品質と適切な利用
- AI生成コードの品質・セキュリティに関する最終責任は開発者にある
- AIが生成したコードは必ず人間がレビューし、品質を確認すること
- 生成コードがプロジェクトの命名規則やコーディング規約に準拠しているか確認すること
- 生成コードの動作を適切にテストすること
- AIの提案を無批判に適用せず、常に批判的思考を持って評価すること
- 生成コードが既存コードベースと整合性があるか確認すること
- 明らかに外部ソースからコピーされたと思われるコードは使用を避けること（ライセンス違反のリスクを防ぐため）
- 不明点がある場合は、チームメンバーやリーダーに相談すること

### 6.3 コンプライアンス・リスク管理
- AIツール利用状況の定期的な監査を実施し、ツールのコンプライアンス状況を確認すること
- AI利用時の操作ログ・監査証跡を適切に記録・保管し、説明責任・トレーサビリティを確保すること
- AI生成物のバイアス検証や説明可能性の確保に努めること

## 7. AIツール活用のワークフロー

### 7.1 AIツールのプロジェクトルール設定
- AIツールの使用開始時に基本的な指示を設定（例：「すべて日本語で回答してください」）
- 専門用語や社内用語の説明をAIに提供
- プロジェクト固有のコーディング規約やスタイルガイドの共有
- 出力形式や詳細度の指定（例：「コメントは日本語で記述」「詳細な説明を含める」）
- 機密情報の取り扱いに関する制約の設定

### 7.2 新機能とカスタマイズ機能開発時
1. 要件の理解とAIを使った実装アプローチの検討
2. AIによるコードスケルトン生成
3. 生成コードの検証と必要な調整
4. コードレビューとリファクタリング
5. 動作検証

### 7.3 バグ修正時
1. バグの再現と原因分析（AIに説明を求める）
2. 修正アプローチの提案をAIから取得
3. 修正コードの生成と適用
4. 動作検証

### 7.4 コードレビュー時
1. AIツールを使用したコード品質チェック
2. 潜在的な問題点や改善点の特定
3. 改善提案の検討と適用
4. レビュー結果のフィードバック

## 8. 効果測定と改善

### 8.1 主な評価指標
- 開発作業の効率化（タスク完了時間の短縮）
- コード品質の向上（バグ発生率の低減、保守性の向上）
- チームメンバーの満足度（定例ミーティングでのフィードバック）
- AIツール活用スキルの向上（プロンプト作成能力、対話スキル）

### 8.2 改善サイクル
- AIツールの使用状況を定期的に確認
- 定例ミーティングでAIツール活用状況の共有
- 効果的な活用事例と課題点の収集

## 9. 教育とナレッジ共有

### 9.1 教育活動

以下の基本的な教育活動を必要に応じて実施します：

- AIツールの基本操作習得（インストール、設定、基本機能）
- 効果的なプロンプト作成方法の共有
- 実際のプロジェクトでの活用事例の紹介
- 定例ミーティングでの活用ノウハウ共有

### 9.2 ナレッジ共有
- 成功事例：AIツール活用の成功事例を社内で共有
- 注意点：誤った使用例や問題点も共有し、リスク認識を高める
- AIツールの新機能や活用方法についての勉強会を定期的に開催  
- 社内Wikiやドキュメント管理システムに活用事例やベストプラクティスを蓄積
### 9.3 定期的なレビュー
- AIツールの活用状況を定期的にレビューし、改善点を洗い出す
### 9.4 フィードバックの収集
- 定期的なアンケートやフィードバックセッションを通じて、AIツールの利用状況や効果を評価
- 開発メンバーからのフィードバックをもとに、ガイドラインや教育内容を改善
### 9.5 サポート体制
- AIツール活用に関する全般的な窓口担当：崔、茂木
- 質問、トラブルシューティング、ツール選定相談などはすべて窓口担当者へ連絡
- 窓口担当者と開発メンバー全員でベストプラクティスを収集・共有

## 10. ガイドラインの更新

本ガイドラインは、技術動向や法令・業界基準の変化、プロジェクトの要件変化に応じて定期的に見直しを行います。更新の提案は開発メンバーから随時受け付け、開発リーダーの承認を経て反映します。

